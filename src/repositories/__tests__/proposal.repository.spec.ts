import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { ProposalRepository } from '../proposal.repository';
import { ProposalEntity } from '../../entities/proposals.entity';
import { TargetRepository } from '../target.repository';

describe('ProposalRepository', () => {
  let proposalRepository: ProposalRepository;
  let mockRepository: jest.Mocked<Repository<ProposalEntity>>;
  let mockTargetRepository: jest.Mocked<TargetRepository>;
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<ProposalEntity>>;

  beforeEach(async () => {
    // Create mock query builder
    mockQueryBuilder = {
      leftJoin: jest.fn().mockReturnThis(),
      innerJoin: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      getCount: jest.fn(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      getRawMany: jest.fn(),
    } as any;

    // Create mock repository
    mockRepository = {
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
      save: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findOne: jest.fn(),
    } as any;

    // Create mock target repository
    mockTargetRepository = {
      deleteTargetsByUids: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProposalRepository,
        {
          provide: getRepositoryToken(ProposalEntity),
          useValue: mockRepository,
        },
        {
          provide: TargetRepository,
          useValue: mockTargetRepository,
        },
      ],
    }).compile();

    proposalRepository = module.get<ProposalRepository>(ProposalRepository);
  });

  describe('findAllProposals', () => {
    it('should use proposalEmployee alias consistently in queries', async () => {
      // Arrange
      const filters = {
        employeeIdentifier: 'test',
        sltName: ['test-uuid'],
        zones: ['GLOBAL'],
        funcs: ['TECHNOLOGY'],
        sltLevel: ['1'],
      };

      mockQueryBuilder.getCount.mockResolvedValue(0);
      mockQueryBuilder.getMany.mockResolvedValue([]);

      // Act
      await proposalRepository.findAllProposals(filters);

      // Assert
      // Verify that the initial query uses 'proposalEmployee' alias
      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('proposal');
      expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('proposal.employee', 'proposalEmployee');

      // Verify that filters use the correct alias
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'proposalEmployee.name LIKE :employeeIdentifier',
        { employeeIdentifier: '%test%' }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'proposalEmployee.zone IN (:...employeeZones)',
        { employeeZones: ['GLOBAL'] }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'proposalEmployee.businessFunction IN (:...employeeFunctions)',
        { employeeFunctions: ['TECHNOLOGY'] }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'proposalEmployee.sltLevel IN (:...sltLevels)',
        { sltLevels: ['1'] }
      );

      // Verify that sltName filter uses proposalEmployee table directly
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'proposalEmployee.employeeUuid IN (:...sltNames)',
        { sltNames: ['test-uuid'] }
      );
    });

    it('should handle numeric employeeIdentifier correctly', async () => {
      // Arrange
      const filters = {
        employeeIdentifier: 12345,
      };

      mockQueryBuilder.getCount.mockResolvedValue(0);
      mockQueryBuilder.getMany.mockResolvedValue([]);

      // Act
      await proposalRepository.findAllProposals(filters);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'proposalEmployee.globalId = :employeeIdentifier',
        { employeeIdentifier: 12345 }
      );
    });
  });

  describe('getFilters', () => {
    it('should use proposalEmployee alias consistently', async () => {
      // Arrange
      mockQueryBuilder.getRawMany.mockResolvedValue([]);

      // Act
      await proposalRepository.getFilters();

      // Assert
      // Verify that getFilters uses only proposalEmployee table
      expect(mockQueryBuilder.innerJoin).toHaveBeenCalledWith('proposal.employee', 'proposalEmployee');
    });

    it('should filter sltNames to only include employees from proposals_employees table with manager-level sltLevels', async () => {
      // Arrange
      mockQueryBuilder.getRawMany.mockResolvedValue([]);

      // Act
      await proposalRepository.getFilters();

      // Assert
      // Verify that sltNames query filters by manager-level sltLevels
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'proposalEmployee.sltLevel IN (:...managerLevels)',
        { managerLevels: ['1', '2', '3'] }
      );
      expect(mockQueryBuilder.select).toHaveBeenCalledWith([
        'DISTINCT proposalEmployee.employeeUuid AS value',
        'proposalEmployee.name AS label'
      ]);
    });
  });
});
